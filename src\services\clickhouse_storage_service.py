"""
ClickHouse Storage Service for KPI Results.

This module implements a dedicated storage service for transferring KPI results
from temporary tables to permanent ClickHouse storage using native operations.

Key Features:
- ClickHouse-native INSERT INTO ... SELECT operations
- Job-level metadata management with status tracking
- Incremental table population strategy
- Support for both pivot and horizontal facts export formats
"""

import json
import logging
import time
from typing import Any, Dict, List, Optional, Tuple


from src.core import config
from src.core.connection import ClickHouseConnection
from src.models.axis import Period
from src.models.kpi import JobParameters, KPIType
from src.services.base_service import BaseService
from src.utils.data_processing import generate_result_id
from src.utils.formatting import NumpyJSONEncoder


class ClickHouseStorageService(BaseService):
    """
    Service for storing KPI results in ClickHouse.

    Stage 1: Query processor creates temporary tables with results
    Stage 2: This service transfers data to permanent storage using native operations
    """

    def __init__(
        self,
        connection: ClickHouseConnection,
        job_parameters: Optional[JobParameters] = None,
        msg_logger_func=None,
    ):
        """
        Initialize the ClickHouse storage service.

        Args:
            connection: ClickHouse connection instance
            job_parameters: JobParameters model containing all validated data
            msg_logger_func: Optional function for logging messages to the UI
        """
        super().__init__(msg_logger_func)
        self.connection = connection
        self.job_parameters = job_parameters
        self.logger = logging.getLogger(__name__)

    def initialize_job_metadata(
        self,
        job_parameters: JobParameters,
        **kwargs,
    ) -> str:
        """
        Initialize job metadata with 'in_progress' status.

        Creates a single metadata record for the entire job (all periods).

        Args:
            job_parameters: JobParameters model containing all validated data
            **kwargs: Additional job parameters

        Returns:
            Combined result ID for the job
        """
        job_id = job_parameters.job_id
        analysis_name = job_parameters.analysis_name
        periods = job_parameters.periods
        kpi_type = job_parameters.kpi_type
        id_panel = job_parameters.id_panel
        axes = job_parameters.axes
        filters = job_parameters.filters

        result_id = generate_result_id(
            job_id=job_id,
            analysis_name=analysis_name,
            period_name=",".join([p.label for p in periods]),
            kpi_type=kpi_type,
        )

        self.logger.info(
            f"Initializing job metadata for job {job_id} with result_id {result_id}"
        )

        job_info = {
            "result_id": result_id,
            "job_id": job_id,
            "analysis_name": analysis_name,
            "id_panel": id_panel,
            "kpi_type": kpi_type.value,
            "periods": [period.label for period in periods],
            "axes": [axis.full_name for axis in axes.values()],
            "filters": [filter.full_name for filter in filters.values()],
            "facts": [fact.display_name for fact in job_parameters.facts_axis],
            "su_fact_name": job_parameters.su_fact_data.name
            if job_parameters.su_fact_data
            else None,
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
        }

        # Add any additional parameters
        job_info.update(kwargs)

        # Insert initial metadata record with 'in_progress' status
        self._insert_job_metadata(
            username=kwargs["username"] or "default_user",
            version_backend=config.,
            result_id=result_id,
            job_id=job_id,
            analysis_name=analysis_name,
            periods=periods,
            kpi_type=kpi_type,
            id_panel=id_panel,
            job_info=job_info,
            status="in_progress",
        )

        return result_id

    def transfer_temp_table_to_storage(
        self,
        temp_table_name: str,
        result_id: str,
        period: Period,
    ) -> Dict[str, Any]:
        """
        Transfer data from temporary table to permanent storage.

        Uses ClickHouse-native INSERT INTO ... SELECT for efficiency.

        Args:
            temp_table_name: Name of the temporary table (without database prefix)
            result_id: Combined result ID for the job
            period: Period information

        Returns:
            Dictionary with transfer status information
        """
        try:
            self.logger.info(
                f"Transferring temp table {temp_table_name} to storage for period {period.label}"
            )

            # Check if temporary table exists
            if not self._table_exists(temp_table_name):
                error_msg = f"Temporary table {temp_table_name} does not exist"
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            # Get table structure from temporary table
            table_structure = self._get_table_structure(temp_table_name)
            if not table_structure:
                error_msg = (
                    f"Failed to get structure of temporary table {temp_table_name}"
                )
                self.logger.error(error_msg)
                return {"success": False, "error": error_msg, "rows_transferred": 0}

            permanent_table = f"kpi_results.data_{result_id}"

            if not self._table_exists(permanent_table):
                # Create the permanent table using the temporary table's structure
                self._create_permanent_table(permanent_table, table_structure)

            # Transfer data using INSERT INTO ... SELECT
            rows_transferred, error = self._transfer_data(
                temp_table_name, permanent_table
            )

            if error:
                return {"success": False, "error": error, "rows_transferred": 0}

            self.logger.info(
                f"Successfully transferred {rows_transferred} rows from {temp_table_name} to permanent storage"
            )

            return {
                "success": True,
                "error": None,
                "rows_transferred": rows_transferred,
                "permanent_table": permanent_table,
            }

        except Exception as e:
            error_msg = f"Failed to transfer temp table {temp_table_name}: {str(e)}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "rows_transferred": 0}

    def finalize_job_storage(
        self,
        job_parameters: JobParameters,
        status: str = "done",
        error_info: Optional[Dict[str, Any]] = None,
        result_table_name: Optional[str] = None,
    ) -> bool:
        """
        Finalize job storage by updating metadata status.

        Args:
            job_parameters: JobParameters model containing all validated data
            status: Final status ('done' or 'error')
            error_info: Optional error information if status is 'error'
            result_table_name: Optional name of the result table

        Returns:
            True if successful, False otherwise
        """
        try:
            result_id = job_parameters.combined_result_id if job_parameters else None
            if not result_id:
                self.logger.error("No result_id provided")
                return False

            completed_at = time.time()

            self.logger.info(
                f"Finalizing job storage for result_id {result_id} with status {status}"
            )

            # Get current job info
            job_info = self._get_job_info(result_id)
            if not job_info:
                self.logger.error(f"No job info found for result_id {result_id}")
                return False

            # Update job info with final status
            job_info["status"] = status

            if error_info:
                job_info["error"] = error_info

            # Calculate total rows if successful
            total_result_rows = job_parameters.result_rows
            final_result_table = result_table_name or ""
            if status == "done":
                job_info["total_rows"] = total_result_rows

            # Update metadata with all parameters as kwargs
            self.update_job_metadata(
                result_id,
                status=status,
                job_duration=f"{job_parameters.job_duration:.2f} ms"
                if job_parameters.job_duration
                else "",
                query_ids=", ".join([str(qid) for qid in job_parameters.query_ids])
                if job_parameters.query_ids
                else "",
                final_result_table=final_result_table,
                result_rows=total_result_rows,
                job_info=job_info,
                completed_at=completed_at,
            )

            self.logger.info(
                f"Successfully finalized job storage for result_id {result_id}"
            )
            return True

        except Exception as e:
            self.logger.error(
                f"Failed to finalize job storage for result_id {result_id}: {str(e)}"
            )
            return False

    def _serialize_axes(self, axes: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize axes configuration for storage."""
        try:
            serialized = {}
            for key, value in axes.items():
                if hasattr(value, "model_dump"):
                    # Pydantic model
                    serialized[key] = value.model_dump()
                elif hasattr(value, "dict"):
                    # Pydantic model (older version)
                    serialized[key] = value.dict()
                else:
                    # Regular dict or other serializable type
                    serialized[key] = value
            return serialized
        except Exception as e:
            self.logger.warning(f"Failed to serialize axes: {e}")
            return {}

    def _serialize_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize filters configuration for storage."""
        try:
            serialized = {}
            for key, value in filters.items():
                if hasattr(value, "model_dump"):
                    # Pydantic model
                    serialized[key] = value.model_dump()
                elif hasattr(value, "dict"):
                    # Pydantic model (older version)
                    serialized[key] = value.dict()
                else:
                    # Regular dict or other serializable type
                    serialized[key] = value
            return serialized
        except Exception as e:
            self.logger.warning(f"Failed to serialize filters: {e}")
            return {}

    def _insert_job_metadata(
        self,
        result_id: str,
        job_id: str,
        username: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: KPIType,
        id_panel: int,
        job_info: Dict[str, Any],
        status: str = "in_progress",
    ):
        """Insert initial job metadata record."""
        try:
            # Prepare data for insertion
            job_info_json = json.dumps(job_info, cls=NumpyJSONEncoder)

            # Escape strings for SQL
            result_id_escaped = self._escape_sql_string(result_id)
            job_id_escaped = self._escape_sql_string(job_id)
            username_escaped = self._escape_sql_string(username)
            analysis_name_escaped = self._escape_sql_string(analysis_name)
            periods_escaped = self._escape_sql_string(periods)
            kpi_type_escaped = self._escape_sql_string(kpi_type.value)
            job_info_escaped = self._escape_sql_string(job_info_json)
            status_escaped = self._escape_sql_string(status)

            # Insert metadata record
            insert_query = f"""
                INSERT INTO kpi_results.results_metadata (
                    id, job_id, analysis_name, periods,
                    kpi_type, id_panel, username, created_at, job_info, status
                )
                VALUES (
                    '{result_id_escaped}', '{job_id_escaped}', '{analysis_name_escaped}',
                    '{periods_escaped}',
                    '{kpi_type_escaped}', {id_panel}, '{username_escaped}', now(), '{job_info_escaped}', '{status_escaped}'
                )
            """

            self.connection.execute_command(insert_query)
            self.logger.info(f"Inserted job metadata for result_id {result_id}")

        except Exception as e:
            self.logger.error(f"Failed to insert job metadata: {str(e)}")
            raise

    def _table_exists(self, table_name: str) -> bool:
        """Check if a table exists."""
        try:
            # Parse table name and optional database name
            if "." in table_name:
                database, table = table_name.split(".", 1)
            else:
                table = table_name
                database = None

            query = f"""
                SELECT 1 FROM system.tables
                WHERE
                name = '{table}'
            """
            if database:
                query += f" AND database = '{database}'"

            result = self.connection.get_query_dataframe(query)
            return not result.empty

        except Exception as e:
            self.logger.error(f"Failed to check if table {table_name} exists: {str(e)}")
            return False

    def _get_table_structure(self, table_name: str) -> Optional[List[Dict[str, str]]]:
        """Get table structure information."""
        try:
            query = f"DESCRIBE TABLE {table_name}"
            result = self.connection.get_query_dataframe(query)

            if result.empty:
                return None

            structure = []
            for _, row in result.iterrows():
                structure.append({"name": row["name"], "type": row["type"]})

            return structure

        except Exception as e:
            self.logger.error(
                f"Failed to get structure of table {table_name}: {str(e)}"
            )
            return None

    def _create_permanent_table(self, table_name: str, structure: List[Dict[str, str]]):
        """Create permanent table with given structure."""
        try:
            # Build column definitions
            column_defs = []
            dimension_cols = []

            for col in structure:
                col_name = (
                    col["name"].replace('"', "").replace("'", "").replace(" ", "_")
                )
                col_type = col["type"]
                column_defs.append(f"`{col_name}` {col_type}")

                # Track dimension columns for ORDER BY
                if col_name.endswith("_position_number"):
                    dimension_cols.append(f"`{col_name}`")

            # Use dimensions for ORDER BY or fallback to tuple()
            order_by = ", ".join(dimension_cols) if dimension_cols else "tuple()"

            # Create table query
            create_query = f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    {", ".join(column_defs)}
                )
                ENGINE = MergeTree()
                ORDER BY ({order_by})
            """

            self.logger.info(f"Creating permanent table: {table_name}")
            self.logger.debug(f"Create table query: {create_query}")

            self.connection.execute_command(create_query)
            self.logger.info(f"Successfully created permanent table {table_name}")

        except Exception as e:
            self.logger.error(
                f"Failed to create permanent table {table_name}: {str(e)}"
            )
            raise

    def _transfer_data(
        self, source_table: str, target_table: str
    ) -> Tuple[Optional[int], Optional[str]]:
        """
        Transfer data between tables and return row count.

        Args:
            source_table: Source table name
            target_table: Target table name

        Returns:
            Number of rows transferred or Error if transfer failed
        """
        try:
            # Use SELECT query to transfer data
            insert_query = f"""
                SELECT * FROM {source_table}
            """

            self.logger.info(f"Transferring data from {source_table} to {target_table}")
            if not self.connection.add_rows_to_table(
                target_table, source_query=insert_query
            ):
                error_msg = (
                    f"Failed to transfer data from {source_table} to {target_table}"
                )
                self.logger.error(error_msg)
                return None, error_msg

            # Count transferred rows
            count_query = f"SELECT count() as cnt FROM {source_table}"
            result = self.connection.get_query_dataframe(count_query)
            row_count = result.iloc[0]["cnt"] if not result.empty else 0

            # Drop source table
            self.connection.drop_temp_table(source_table)

            self.logger.info(f"Transferred {row_count} rows from {source_table}")

            return row_count, None

        except Exception as e:
            self.logger.error(
                f"Failed to transfer data from {source_table} to {target_table}: {str(e)}"
            )
            raise

    def _get_job_info(self, result_id: str) -> Optional[Dict[str, Any]]:
        """Get job info from metadata."""
        try:
            query = f"""
                SELECT job_info FROM kpi_results.results_metadata
                WHERE id = '{self._escape_sql_string(result_id)}'
            """

            result = self.connection.get_query_dataframe(query)
            if result.empty:
                return None

            job_info_str = result.iloc[0]["job_info"]
            return json.loads(job_info_str)

        except Exception as e:
            self.logger.error(
                f"Failed to get job info for result_id {result_id}: {str(e)}"
            )
            return None

    def _count_table_rows(self, table_name: str) -> int:
        """Count rows in a table."""
        try:
            query = f"SELECT count() as cnt FROM {table_name}"
            result = self.connection.get_query_dataframe(query)
            return result.iloc[0]["cnt"] if not result.empty else 0

        except Exception as e:
            self.logger.error(f"Failed to count rows in table {table_name}: {str(e)}")
            return 0

    def update_job_metadata(
        self,
        result_id: str,
        **update_params: Dict[str, Any],
    ):
        """
        Update job metadata with provided parameters.

        Args:
            result_id: The ID of the result to update
            **update_params: Dictionary of column names and values to update
        """
        try:
            result_id_escaped = self._escape_sql_string(result_id)

            # Process special cases like job_info that needs JSON serialization
            if "job_info" in update_params:
                update_params["job_info"] = json.dumps(
                    update_params["job_info"], cls=NumpyJSONEncoder
                )

            # Build update expressions
            update_expressions = []
            for column, value in update_params.items():
                if isinstance(value, str):
                    value = self._escape_sql_string(value)
                    update_expressions.append(f"{column} = '{value}'")
                elif isinstance(value, (int, float)):
                    update_expressions.append(f"{column} = {value}")
                else:
                    value = self._escape_sql_string(str(value))
                    update_expressions.append(f"{column} = '{value}'")

            update_query = f"""
                ALTER TABLE kpi_results.results_metadata
                UPDATE {", ".join(update_expressions)}
                WHERE id = '{result_id_escaped}'
            """

            self.connection.execute_command(update_query)
            self.logger.info(
                f"Updated job metadata for result_id {result_id} with parameters: {list(update_params.keys())}"
            )

        except Exception as e:
            self.logger.error(
                f"Failed to update job metadata for result_id {result_id}: {str(e)}"
            )
            raise

    def _ensure_results_metadata_table(self):
        """Ensure the results_metadata table exists with the new schema."""
        try:
            # Load and execute the SQL schema file
            import os

            sql_file_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "sql",
                "results_metadata_table.sql",
            )

            if os.path.exists(sql_file_path):
                with open(sql_file_path, "r", encoding="utf-8") as f:
                    sql_content = f.read()

                # Execute the SQL content (may contain multiple statements)
                # Split by semicolon and execute each statement
                statements = [
                    stmt.strip() for stmt in sql_content.split(";") if stmt.strip()
                ]
                for statement in statements:
                    if statement and not statement.startswith("--"):
                        self.connection.execute_command(statement)

                self.logger.debug(
                    "Ensured results_metadata table exists using SQL file"
                )
            else:
                # Fallback to inline SQL if file doesn't exist
                self.logger.warning(
                    f"SQL file not found: {sql_file_path}, using inline SQL"
                )
                self._create_results_metadata_table_inline()

        except Exception as e:
            self.logger.error(f"Failed to ensure results_metadata table: {str(e)}")
            # Try fallback
            try:
                self._create_results_metadata_table_inline()
            except Exception as fallback_error:
                self.logger.error(f"Fallback also failed: {str(fallback_error)}")
                raise e

    def _escape_sql_string(self, value: str) -> str:
        """Escape a string for safe SQL insertion."""
        if value is None:
            return ""

        # Convert to string if not already
        str_value = str(value)

        # Escape single quotes by doubling them
        escaped = str_value.replace("'", "''")

        # Escape backslashes
        escaped = escaped.replace("\\", "\\\\")

        return escaped

    def get_job_status(self, result_id: str) -> Optional[str]:
        """
        Get the current status of a job.

        Args:
            result_id: Combined result ID for the job

        Returns:
            Job status ('in_progress', 'done', 'error') or None if not found
        """
        try:
            query = f"""
                SELECT status FROM kpi_results.results_metadata
                WHERE id = '{self._escape_sql_string(result_id)}'
            """

            result = self.connection.get_query_dataframe(query)
            if result.empty:
                return None

            return result.iloc[0]["status"]

        except Exception as e:
            self.logger.error(
                f"Failed to get job status for result_id {result_id}: {str(e)}"
            )
            return None
