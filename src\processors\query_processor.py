import logging
import time
import uuid
import copy
from typing import Optional, List, Tuple, Dict, Any, Union

from src.core.connection import ClickHouseConnection
from src.queries.query_builder import ClickHouseQuery
from src.services.clickhouse_storage_service import ClickHouseStorageService
from src.models.kpi import JobParameters, KPIType
from src.utils.job_api_client import set_job_progress_message
from src.models.axis import Period
from src.core.exceptions import QueryError
from src.utils.data_processing import calculate_ddl_chunk_indices


class QueryProcessor:
    """Class to handle query processing for KPI analysis."""

    def __init__(
        self,
        connection: ClickHouseConnection,
        query_builder: ClickHouseQuery,
        storage_service: Optional[ClickHouseStorageService] = None,
    ):
        """
        Initialize QueryProcessor.

        Args:
            connection: ClickHouse database connection
            query_builder: Query builder instance
            storage_service: Storage service

        Raises:
            ValueError: If any required parameter is None
        """
        # Validate required parameters
        if not connection:
            raise ValueError("ClickHouse connection is required")
        if not query_builder:
            raise ValueError("Query builder is required")

        # Store dependencies
        self.connection = connection
        self.query_builder = query_builder

        # Create storage service
        self.storage_service = storage_service or ClickHouseStorageService(connection)

        # Initialize logger and state
        self.logger = logging.getLogger(self.__class__.__name__)
        self.temp_tables: List[str] = []

    def _create_temp_table(
        self, table_name: str, query: str, query_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Create a temporary table and track it.

        Args:
            table_name: Name of the temporary table to create
            query: SQL query to populate the table
            query_id: Optional custom query ID for tracking

        Returns:
            Table name if successful, None if failed
        """
        self.logger.info(f"Creating temporary table '{table_name}'")
        settings = {"query_id": query_id} if query_id else None

        try:
            result = self.connection.create_temp_table(
                query, table_name, "Memory", None, settings
            )

            # Check if the result is a tuple indicating an error
            if isinstance(result, tuple) and len(result) == 2 and result[0] == "ERROR":
                error_msg = f"Error creating temp table {table_name}: {result[1]}"
                self.logger.error(error_msg)
                return None

            # Add to tracking list
            self.temp_tables.append(table_name)
            self.logger.info(f"Temporary table '{table_name}' created successfully")
            return table_name

        except Exception as e:
            self.logger.error(f"Exception creating temp table {table_name}: {e}")
            return None

    def _drop_temp_table(self, table_name: str) -> bool:
        """
        Drop a specific temporary table.

        Args:
            table_name: Name of the temporary table to drop

        Returns:
            True if successful, False otherwise
        """
        if table_name not in self.temp_tables:
            self.logger.warning(
                f"Temporary table '{table_name}' not found in tracking list"
            )
            return False

        try:
            self.logger.info(f"Dropping temporary table '{table_name}'")
            result = self.connection.drop_temp_table(table_name)

            if result:
                self.temp_tables.remove(table_name)
                self.logger.info(f"Temporary table '{table_name}' dropped successfully")
                return True
            else:
                self.logger.warning(f"Failed to drop temporary table '{table_name}'")
                return False

        except QueryError as e:
            # Log the error but continue
            self.logger.warning(
                f"ClickHouse error dropping temporary table '{table_name}': {e}"
            )
            if hasattr(e, "error_code"):
                self.logger.warning(
                    f"Error code: {e.error_code}, type: {getattr(e, 'error_type', 'unknown')}"
                )
            return False
        except Exception as e:
            self.logger.warning(f"Failed to drop temporary table '{table_name}': {e}")
            return False

    def _generate_chunk_queries(
        self,
        period: Period,
        kpi_type: KPIType,
        query_id_prefix: str,
    ) -> Tuple[List[str], List[str], List[Tuple[int, int]]]:
        """
        Generate SQL templates for each chunk and return final queries and query IDs.

        Args:
            period: Period model containing chunking configuration
            kpi_type: Type of KPI to process
            query_id_prefix: Prefix for generating query IDs

        Returns:
            Tuple of (final_queries, final_chunk_query_ids, chunk_indices)
        """
        fourth_axis_data = self.query_builder.axes["fourth_axis"]
        ddl_queries = fourth_axis_data.ddl["queries"]
        total_ddls = len(ddl_queries)

        # Get chunking parameters
        total_positions_product = getattr(period, "total_positions", None)
        threshold = getattr(period, "threshold", None)
        fourth_axis_positions = fourth_axis_data.axis_positions

        # Validate parameters before proceeding
        if total_positions_product is None:
            raise ValueError("total_positions not found in period model")
        if threshold is None:
            raise ValueError("threshold not found in period model")
        if fourth_axis_positions is None or (
            isinstance(fourth_axis_positions, list) and len(fourth_axis_positions) == 0
        ):
            raise ValueError("fourth_axis.axis_positions is None or empty")

        # Extract the actual axis positions value (it's a list, we need the first element)
        if isinstance(fourth_axis_positions, int):
            axis_positions_value = fourth_axis_positions
        elif isinstance(fourth_axis_positions, list) and len(fourth_axis_positions) > 0:
            axis_positions_value = fourth_axis_positions[0]
        else:
            raise ValueError("fourth_axis.axis_positions has invalid type or value")

        if axis_positions_value == 0:
            raise ValueError("fourth_axis.axis_positions value is 0")

        # Calculate chunk indices using the utility function
        chunk_indices = calculate_ddl_chunk_indices(
            total_positions=total_positions_product,
            axis_positions=axis_positions_value,
            threshold=threshold,
            total_ddls=total_ddls,
        )

        # Log chunking information
        other_axes_positions_product = total_positions_product // axis_positions_value
        max_fourth_axis_positions_per_chunk = threshold // other_axes_positions_product
        max_fourth_axis_positions_per_chunk = max(
            1, max_fourth_axis_positions_per_chunk
        )

        self.logger.info(f"Total positions (from period): {total_positions_product}")
        self.logger.info(f"Fourth axis positions: {fourth_axis_positions}")
        self.logger.info(
            f"Other axes positions product: {other_axes_positions_product}"
        )
        self.logger.info(
            f"Max fourth_axis positions per chunk: {max_fourth_axis_positions_per_chunk}"
        )
        self.logger.info(
            f"Splitting {total_ddls} DDL queries into {len(chunk_indices)} chunks"
        )

        # Store original fourth_axis data
        original_fourth_axis = copy.deepcopy(fourth_axis_data)

        final_queries = []
        final_chunk_query_ids = []

        # Generate queries for each chunk
        for chunk_idx, (start_idx, end_idx) in enumerate(chunk_indices):
            chunk_ddls = ddl_queries[start_idx:end_idx]

            # Calculate estimated total positions for this chunk
            estimated_fourth_axis_positions = len(chunk_ddls)
            estimated_total_positions = (
                estimated_fourth_axis_positions * other_axes_positions_product
            )

            self.logger.info(
                f"Generating query for chunk {chunk_idx + 1}/{len(chunk_indices)}: DDLs {start_idx} to {end_idx - 1}"
            )
            self.logger.info(
                f"Estimated positions in chunk: {estimated_fourth_axis_positions} (fourth_axis) * {other_axes_positions_product} (others) = {estimated_total_positions} total"
            )

            # Create a deep copy of the original fourth_axis AxisData
            fourth_axis_copy = copy.deepcopy(original_fourth_axis)
            fourth_axis_copy.ddl["queries"] = chunk_ddls

            # Temporarily set the sliced copy in query_builder
            self.query_builder.axes["fourth_axis"] = fourth_axis_copy

            # Generate final_query_chunk using the sliced fourth_axis
            final_query_chunk = self.query_builder.query_final(
                catman=True if kpi_type == KPIType.CATMAN_KPI else False
            )

            # Generate custom query ID for this chunk's final query
            final_chunk_query_id = (
                f"{query_id_prefix}final_chunk_{chunk_idx}_{uuid.uuid4().hex[:8]}"
            )

            final_queries.append(final_query_chunk)
            final_chunk_query_ids.append(final_chunk_query_id)

        # Restore the original fourth_axis AxisData
        self.query_builder.axes["fourth_axis"] = original_fourth_axis

        return final_queries, final_chunk_query_ids, chunk_indices

    def _process_chunks(
        self,
        final_queries: List[str],
        final_chunk_query_ids: List[str],
    ) -> Tuple[List[str], Optional[Dict[str, Any]]]:
        """
        Execute the chunked query processing workflow.

        Args:
            final_queries: List of final SQL queries for each chunk
            final_chunk_query_ids: List of query IDs for final queries

        Raises:
            QueryError: If any query fails

        Returns:
            Tuple of (List of query ids and Error info if failed or None)
        """
        num_chunks = len(final_queries)
        query_ids = []

        # Process each chunk
        try:
            for chunk_idx, (final_query_chunk, final_chunk_query_id) in enumerate(
                zip(final_queries, final_chunk_query_ids)
            ):
                # For first chunk: create final result table, for subsequent chunks: insert directly
                if chunk_idx == 0:
                    # First chunk: Create final table for this chunk's final data
                    self.logger.info(f"Processing chunk {chunk_idx + 1}/{num_chunks}")
                    chunk_table = self._create_temp_table(
                        "final",
                        final_query_chunk,
                        final_chunk_query_id,
                    )
                    query_ids.append(final_chunk_query_id)
                    if not chunk_table:
                        error_msg = "Failed to create final table, cannot continue"
                        self.logger.error(error_msg)
                        raise QueryError(error_msg)

                else:
                    # Subsequent chunks: Insert directly into existing final result table
                    self.logger.info(f"Processing chunk {chunk_idx + 1}/{num_chunks}")
                    insert_settings = {"query_id": final_chunk_query_id}
                    insert_query = self.connection.add_rows_from_query(
                        "final", final_query_chunk, insert_settings
                    )
                    query_ids.append(final_chunk_query_id)
                    if insert_query:
                        self.logger.info(
                            f"Chunk {chunk_idx + 1}/{num_chunks} processed successfully"
                        )
                    else:
                        error_msg = f"Failed to insert data from chunk {chunk_idx + 1}/{num_chunks} into final table from query {final_query_chunk}, cannot continue"
                        self.logger.error(error_msg)
                        raise QueryError(error_msg)

                    self.logger.info(
                        f"Chunk {chunk_idx + 1}/{num_chunks} inserted into final table"
                    )

            self.logger.info(f"All {num_chunks} chunks processed successfully")
            return query_ids, None
        except Exception as e:
            # Log the error
            error_message = f"Failed to process chunks: {e}"
            self.logger.error(error_message)
            return query_ids, {"error": error_message}

    def _cleanup_temp_tables(self, temp_tables: Optional[List[str]] = None) -> None:
        """
        Clean up temporary tables.

        Args:
            temp_tables: List of temporary table names to drop. If None, all tables in self.temp_tables will be dropped.
        """
        # Determine which tables to drop
        tables_to_drop = temp_tables if temp_tables is not None else self.temp_tables[:]

        # Skip if no tables to drop
        if not tables_to_drop:
            self.logger.debug("No temporary tables to clean up")
            return

        self.logger.info(f"Cleaning up {len(tables_to_drop)} temporary tables")

        # Drop each table
        for table in tables_to_drop[:]:
            self._drop_temp_table(table)

        # Log remaining tables
        if self.temp_tables:
            self.logger.debug(
                f"Remaining temporary tables: {', '.join(self.temp_tables)}"
            )

    def process_query(
        self,
        job_parameters: JobParameters,
        queries: Dict[str, Union[List[str], List[List[str]]]],
    ) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        Process a KPI query and store the results directly in ClickHouse.

        Args:
            job_parameters: JobParameters model containing all validated data
            queries: Dictionary mapping query names to either [query_string, query_id] or [query_strings, query_ids] for chunked queries

        Returns:
            Tuple of (Temporary table name if successful or None, Error info if failed or None)
        """
        # Start tracking total job execution time
        job_start_time = time.time()

        # Track all query IDs
        query_ids = []

        try:
            if not self.connection.execute_command(queries["max_query_size"][0]):
                error_msg = "Failed to set max_query_size, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            if not self.connection.execute_command(queries["max_ast_elements"][0]):
                error_msg = "Failed to set max_ast_size, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            if not self.connection.execute_command(
                queries["optimize_aggregation_in_order"][0]
            ):
                error_msg = (
                    "Failed to set optimize_aggregation_in_order, cannot continue"
                )
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 1. Create 'pre_axis' temporary table
            pre_axis_table = self._create_temp_table(
                "pre_axis", queries["pre_axis"][0], queries["pre_axis"][1]
            )
            job_parameters.extend(query_ids=queries["pre_axis"][1])
            if not pre_axis_table:
                error_msg = "Failed to create pre_axis table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 2. Create 'axis' temporary table (which depends on 'pre_axis')
            axis_table = self._create_temp_table(
                "axis", queries["axis"][0], queries["axis"][1]
            )
            job_parameters.extend(query_ids=queries["axis"][1])
            if not axis_table:
                error_msg = "Failed to create axis table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 3. Drop 'pre_axis' temporary table after 'axis' is created but before 'buyers_final' is created
            self._drop_temp_table("pre_axis")

            # 4. Create 'buyers_final' temporary table (which depends on 'axis')
            buyers_table = self._create_temp_table(
                "buyers_final", queries["buyers"][0], queries["buyers"][1]
            )
            job_parameters.extend(query_ids=queries["buyers"][1])
            if not buyers_table:
                error_msg = "Failed to create buyers_final table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 5. Drop axis table as it's no longer needed
            self._drop_temp_table("axis")

            # 6. Create 'Buyers_Uplift_Factor' (BUF) table if needed
            buf_table = None
            if "BUF" in self.query_builder.required_facts:
                buf_table = self._create_temp_table(
                    "Buyers_Uplift_Factor", queries["buf"][0], queries["buf"][1]
                )
                job_parameters.extend(query_ids=queries["buf"][1])
                if not buf_table:
                    error_msg = (
                        "Failed to create Buyers_Uplift_Factor table, cannot continue"
                    )
                    self.logger.error(error_msg)
                    raise QueryError(error_msg)

            # 7. Create final temporary table (which depends on 'buyers_final' and 'Buyers_Uplift_Factor' if applicable)
            if job_parameters.period.split_axis == "fourth_axis":
                self.logger.info(
                    f"Processing {job_parameters.period.split_axis} with chunking, threshold: {job_parameters.period.threshold}"
                )
                chunk_query_ids, error_info = self._process_chunks(
                    queries["final"][0], queries["final"][1]
                )
                query_ids.extend(chunk_query_ids)
            else:
                final_table = self._create_temp_table(
                    "final", queries["final"][0], queries["final"][1]
                )
                job_parameters.extend(query_ids=queries["final"][1])
                if not final_table:
                    error_msg = "Failed to create final table, cannot continue"
                    self.logger.error(error_msg)
                    raise QueryError(error_msg)

            # 8. Drop BUF table if it was created and buyers_final
            if buf_table:
                self._drop_temp_table("Buyers_Uplift_Factor")
            self._drop_temp_table("buyers_final")

            # 9. Create result table while all required tables are still available
            result_table = self._create_temp_table(
                f"{job_parameters.combined_result_id}",
                queries["result_table"][0],
                queries["result_table"][1],
            )
            job_parameters.extend(query_ids=queries["result_table"][1])
            if not result_table:
                error_msg = (
                    "Failed to create temporary table for result, cannot continue"
                )
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 10. Drop final table
            self._drop_temp_table("final")

            # Transfer to permanent storage using storage service
            self.logger.info(
                f"Transferring final result from {result_table} to permanent storage"
            )

            transfer_result = self.storage_service.transfer_temp_table_to_storage(
                temp_table_name=result_table,
                result_id=job_parameters.combined_result_id,
                period=job_parameters.period,
            )

            # Update total rows in job parameters (if not already set)
            rows_transferred = (
                transfer_result["rows_transferred"] if transfer_result["success"] else 0
            )  # Handle transfer failure
            job_parameters.result_rows += rows_transferred or 0

            # Drop all remaining temporary tables
            self._cleanup_temp_tables()

            if not transfer_result["success"]:
                error_msg = f"Failed to transfer temporary table to storage: {transfer_result['error']}"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            self.logger.info(
                f"Transferred {transfer_result['rows_transferred']} rows to permanent storage"
            )

            # Calculate job duration
            job_duration_ms = (time.time() - job_start_time) * 1000
            self.logger.info(f"Result completed in {job_duration_ms:.2f} ms")

            return transfer_result["permanent_table"], None

        except Exception as e:
            # Calculate job duration for error case
            job_duration_ms = (time.time() - job_start_time) * 1000

            # Extract error code if available
            error_code = getattr(e, "error_code", None)
            error_message = str(e)

            if not error_code:  # If not directly available, try regex
                # Try to extract error code from the message using different patterns
                import re  # Import locally to avoid unused global import warning if not needed

                # Pattern 1: "error code 62"
                match = re.search(r"error code (\d+)", error_message.lower())
                if match:
                    error_code = int(match.group(1))
                else:
                    # Pattern 2: "Code: 62."
                    match = re.search(r"Code: (\d+)[.\s]", error_message)
                    if match:
                        error_code = int(match.group(1))

            # Log the extracted error code
            if error_code:
                self.logger.info(f"Extracted ClickHouse error code: {error_code}")

            # Create error info for storage
            error_info = {
                "message": error_message,
                "error_code": error_code,
                "period": job_parameters.period.label,
                "job_duration_ms": job_duration_ms,
            }

            self.logger.error(
                f"Result processing failed for period {job_parameters.period.label}: {error_message}"
            )

            return None, error_info

        finally:
            # Calculate total job duration
            total_job_duration_ms = (time.time() - job_start_time) * 1000
            self.logger.info(f"Total job duration: {total_job_duration_ms:.2f} ms")

            # Clean up any remaining temporary tables that weren't explicitly dropped
            if self.temp_tables:
                self.logger.info(
                    f"Cleaning up {len(self.temp_tables)} remaining temporary tables"
                )
                self._cleanup_temp_tables(self.temp_tables)
